import { useEffect, useState } from 'react';
import { View } from '@instructure/ui-view';
import { Text } from '@instructure/ui-text';
import { Img } from '@instructure/ui-img';
import { Link } from '@instructure/ui-link';

import * as API from "../../utils/api";
import { Heading } from '@instructure/ui-heading';

const ResourcesTab = (props) => {
  const [resources, setResources] = useState([]);

  useEffect(() => {
    getExtResourceList();
  }, []);

  const getExtResourceList = () => {
    API.getExternalResources()
      .then((response) => response.data)
      .then((response) => {
        setResources(response.external_resources);
      })
      .catch((error) => {
        console.log(error);
      });
  }

  const renderCard = (resource) => {
    return (
      <Link href={resource.base_url} isWithinText={false} target="_blank">
      <View
        as="div"
        display="inline-block"
        minHeight="17rem"
        margin="small"
        textAlign="center"
        shadow="above"
        background="primary"
        borderRadius="medium"
      >
        <View as="div"
          background="info"
          height="9rem"
          width="16rem"
          textAlign="center"
          overflowX="auto"
          overflowY="auto"
        >
          <Img src={resource.image_url} alt={resource.file_name} constrain="cover"/>
        </View>
        <View as="div"
          padding="x-small"
          minHeight="6rem"
          width="16rem"
          textAlign="start"
          overflowX="auto"
          overflowY="auto"
        >{resource.description}</View>
      </View>
      </Link>
    )
  }

  const renderResources = () => {
    return resources.map(res => {
      return renderCard(res)
    })
  }

  return (
    <View as="div" textAlign="start" className='header-text' padding='medium'>
      <Heading level="h2" margin="0 0 medium 0">
        My Global Resources ({resources.length})
      </Heading>
      { renderResources() }
    </View>
  )
}

export default ResourcesTab
