import { useEffect, useState } from 'react';

import { View } from '@instructure/ui-view';
import { Flex } from '@instructure/ui-flex';
import { Text } from '@instructure/ui-text';
import { ProgressBar } from '@instructure/ui-progress';
import { DateInput2 } from '@instructure/ui-date-input';
import { IconButton } from '@instructure/ui-buttons';
import { Spinner } from '@instructure/ui-spinner';
import CelebrationOverlay from '../../shared/components/CelebrationOverlay';
import { Modal } from '@instructure/ui-modal';
import { Button } from '@instructure/ui-buttons';

import {
  IconArrowOpenEndLine,
  IconArrowOpenStartLine,
} from '@instructure/ui-icons';

import * as API from "../../utils/api";
import * as Util from "../../utils/helpers";
import DayItemsList from './DayItemsList';

const DayView = ({ studentId, setBubbleMessage, currentTheme, themeAssets, isK5Student=false, hasPastDueItems = false, setSelectedTab }) => {
  const [dueAssignments, setDueAssignments] = useState([]);
  const [calendarEvents, setCalendarEvents] = useState([]);
  const [inputDate, setInputDate] = useState(new Date().toDateString());
  const [prevInteraction, setPrevInteraction] = useState('enabled');
  const [nextInteraction, setNextInteraction] = useState('enabled');
  const [minDate, setMinDate] = useState();
  const [maxDate, setMaxDate] = useState();
  const [dateError, setDateError] = useState([]);
  const [isReady, setIsReady] = useState(false);
  const [loading, setLoading] = useState(false);
  const [refetchFlag, setRefetchFlag] = useState(false);
  const [combinedItems, setCombinedItems] = useState([]);
  const [showFireworks, setShowFireworks] = useState(false);
  const [showCongratsModal, setShowCongratsModal] = useState(false);

  const parsedDate = (dateStr) => Util.setDateOnZeroHour(dateStr);

  const fetchData = () => {
    setLoading(true);
    API.getStudentDayDueAssignments(studentId, { due_date: inputDate })
      .then((response) => response.data)
      .then((response) => {
        setDueAssignments(response.assignments || []);
        setCalendarEvents(Object.values(response.calendar_events)[0] || []);
        if (response.show_fireworks) {
          setShowFireworks(response.show_fireworks);
        }
        setLoading(false);
      })
      .catch((error) => {
        console.log(error);
      });
  }

  useEffect(() => {
    if (!setBubbleMessage) return;

    const total = dueAssignments.length;
    const completed = dueAssignments.filter(a => a.req_status === 'mastered').length;
    const progress = total === 0 ? 0 : (completed / total) * 100;

    if (progress === 100) {
      setBubbleMessage(`Great work! You finished all of today's lessons`);
    } else if (progress < 100 && total === 0) {
      setBubbleMessage(`Hi ${window.ENV.user.first_name}`);
    }
  }, [dueAssignments, studentId, setBubbleMessage]);

  useEffect(() => {
    if (refetchFlag) {
      fetchData();
      setRefetchFlag(false);
    }
  }, [refetchFlag]);

  useEffect(() => {
    fetchData();
  }, [studentId, inputDate]);

  useEffect(() => {
    const items = [
      ...(dueAssignments || []).map((assignment) => ({
        ...assignment,
        sortTime: assignment.submission_due_at ? new Date(assignment.submission_due_at).getTime() : Infinity,
      })),
      ...(calendarEvents || []).map((event) => ({
        ...event,
        item_type: 'calendar_event',
        sortTime: event.start_at ? new Date(event.start_at).getTime() : Infinity,
        id: event.canvas_id,
      })),
    ].sort((a, b) => a.sortTime - b.sortTime);

    setCombinedItems(items);
  }, [dueAssignments, calendarEvents]);

  useEffect(() => {
    API.getStudentDateLimits(studentId)
      .then(res => res.data)
      .then(res => {
        const start = [res.course_start, res.term_start].filter(Boolean).map(parsedDate);
        const end = [res.course_end, res.term_end].filter(Boolean).map(parsedDate);
        setMinDate(new Date(Math.min(...start)));
        setMaxDate(new Date(Math.max(...end)));
      })
      .catch(console.log);
  }, [studentId]);

  useEffect(() => {
    if (minDate && maxDate) {
      calendarInteraction(inputDate);
      setIsReady(true);
    }
  }, [minDate, maxDate]);

  const modifyDay = (dateStr, type, step) => {
    const d = new Date(dateStr);
    d.setUTCDate(d.getUTCDate() + step);
    setInputDate(d.toDateString());
    calendarInteraction(d);
  };

  const calendarInteraction = (dateStr) => {
    const date = parsedDate(dateStr);

    if (date.getTime() < minDate.getTime()) {
      setPrevInteraction('disabled');
      setNextInteraction('enabled');
      setDateError([{ type: 'error', text: 'Date is outside of the course & term start date.' }]);
    } else if (date.getTime() > maxDate.getTime()) {
      setPrevInteraction('enabled');
      setNextInteraction('disabled');
      setDateError([{ type: 'error', text: 'Date is outside of the course & term end date.' }]);
    } else {
      setPrevInteraction('enabled');
      setNextInteraction('enabled');
      setDateError([]);
    }
  };

  const dateLabelValue = (dateInput) => {
    let label = dateInput;
    if (Util.isWeekend(dateInput)) label += " [Weekend]";
    if (Util.isBlackoutDate(dateInput)) label += " (Blackout)";
    return label;
  };

  const handleDateValidation = (e, inputValue, utcIsoDate) => {
    const date = new Date(inputValue);
    calendarInteraction(date);
  };

  const handleOnCloseFireworks = () => {
    setShowFireworks(false);
    setShowCongratsModal(true);
    API.updateStudentCelebrationEvent(studentId, { due_date: inputDate })
      .then(() => {
        console.log('Celebration event updated successfully');
      })
      .catch((error) => {
        console.log('Error updating celebration event:', error);
      });
  }

  const renderProgress = () => {
    const total = dueAssignments.length;
    const completed = dueAssignments.filter(a => a.req_status === 'mastered').length;
    const percent = total === 0 ? 0 : Math.round((completed / total) * 100);

    return (
      <Flex alignItems='center' justifyItems="space-between" position="relative" id='progress-bar'>
        <Flex.Item padding="x-small">
          <Text size="small">Progress:</Text>
        </Flex.Item>
        <Flex.Item padding="x-small" shouldGrow shouldShrink>
          <ProgressBar
            screenReaderLabel="Loading completion"
            valueNow={completed}
            valueMax={total}
            renderValue={() => <Text>{percent}%</Text>}
            margin="0 0"
            themeOverride={{ trackColor: '#2d3b4514' }}
            id='progress-bar-value'
          />
        </Flex.Item>
        {isK5Student && themeAssets.elements.completionArt && percent === 100 && (
          <Flex.Item padding="x-small" shouldShrink id='completion-art'>
            <img
              src={themeAssets.elements.completionArt}
              alt="Completion"
              style={{ height: 30 }}
            />
          </Flex.Item>
        )}
      </Flex>
    );
  };


  const renderDatePicker = () => (
    <Flex alignItems="start">
      <Flex.Item>
        <IconButton
          screenReaderLabel="Prev"
          onClick={() => modifyDay(inputDate, 'day', -1)}
          interaction={prevInteraction}
          margin="none xx-small"
          focusColor='inverse'
        >
          <IconArrowOpenStartLine />
        </IconButton>
      </Flex.Item>
      <Flex.Item>
        <DateInput2
          renderLabel=""
          screenReaderLabels={{
            calendarIcon: 'Calendar',
            nextMonthButton: 'Next month',
            prevMonthButton: 'Previous month'
          }}
          value={dateLabelValue(inputDate)}
          width="23rem"
          locale="en-us"
          dateFormat={{
            parser: (input) => {
              const newDate = new Date(input);
              return isNaN(newDate) ? '' : newDate;
            },
            formatter: (newDate) => newDate.toDateString()
          }}
          onChange={(e, inputValue) => setInputDate(inputValue)}
          invalidDateErrorMessage="Invalid date"
          onRequestValidateDate={handleDateValidation}
          messages={dateError}
        />
      </Flex.Item>
      <Flex.Item>
        <IconButton
          screenReaderLabel="Next"
          onClick={() => modifyDay(inputDate, 'day', 1)}
          interaction={nextInteraction}
          margin="none xx-small"
          focusColor='inverse'
        >
          <IconArrowOpenEndLine />
        </IconButton>
      </Flex.Item>
    </Flex>
  );

  const renderBlackoutDayRowItem = (dateStr) =>
    Util.isBlackoutDate(dateStr) && (
      <View as="div" borderWidth="small">
        <Flex direction="column">
          <Flex.Item>
            <View as="div" background="secondary">
              <Text size="medium">Blackout Day</Text>
            </View>
          </Flex.Item>
        </Flex>
      </View>
    );

  const renderDayViewBody = () => (
    <View as="div" display='block' padding='small'>
      {renderBlackoutDayRowItem(inputDate)}
      {loading ? (
        <Spinner renderTitle="Loading" size="medium" />
      ) : combinedItems.length > 0 ? (
        <DayItemsList studentId={studentId} combinedItems={combinedItems} setRefetchFlag={setRefetchFlag} />
      ) : (
        <View as="div" textAlign="start">
          <Text size="medium" color="secondary">No Items Due</Text>
        </View>
      )}
    </View>
  );

  return (
    <View as="div">
      <View as="div" display='block' padding='small'>
        <Flex>
          <Flex.Item textAlign='start' size="60%">
            {isReady && renderDatePicker()}
          </Flex.Item>
          <Flex.Item size="40%">
            {dueAssignments.length > 0 && renderProgress()}
          </Flex.Item>
        </Flex>
      </View>
      {renderDayViewBody()}

      { isK5Student &&
      <>
        <CelebrationOverlay
          open={showFireworks}
          onClose={() => handleOnCloseFireworks()}
          gifSrc={themeAssets.elements?.gif}
          videoSrc={themeAssets.elements?.celebrationVideo}
          audioSrc={themeAssets.elements?.celebrationAudio}
          playAudio={window.ENV.user_config.audio_enabled}
        />

        {/* Post Celebration Animation Modal for Past Due Items */}
        <Modal
          open={showCongratsModal}
          onDismiss={() => {
            setSelectedTab('past_due_view')
            setShowCongratsModal(false)}
          }
          size="small"
          label="Notification"
          shouldCloseOnDocumentClick={false}
          shouldCloseOnEscape={false}
        >
          <Modal.Body>
            <Text size="medium">
              {hasPastDueItems
                ? "You finished today's plan! You still have some Past Due items to complete."
                : "Congratulations! You finished all of today's plan and have no Past Due items!"}
            </Text>
          </Modal.Body>
          <Modal.Footer>
            <Button
              color="primary"
              type="button"
              onClick={() => {
                setSelectedTab('past_due_view')
                setShowCongratsModal(false)
                }}
            >
              OK
            </Button>
          </Modal.Footer>
        </Modal>
      </>}
    </View>
  );
};

export default DayView;
